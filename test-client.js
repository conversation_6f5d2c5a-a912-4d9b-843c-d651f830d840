/**
 * Test Client for LockBlock Server
 * Run this to test the simple server implementation
 */

import SimpleLockBlockClient from './api-lockblock/client/src/simpleLockBlockClient.js';

async function testMultipleClients() {
  console.log('🎮 Testing LockBlock Server with Multiple Clients\n');
  
  // Create two clients with different private keys
  const client1 = new SimpleLockBlockClient('ws://localhost:8080', '0x' + '1'.repeat(64));
  const client2 = new SimpleLockBlockClient('ws://localhost:8080', '0x' + '2'.repeat(64));
  
  try {
    console.log('👤 Client 1 connecting...');
    await client1.connect();
    await client1.authenticate();
    
    console.log('\n👤 Client 2 connecting...');
    await client2.connect();
    await client2.authenticate();
    
    console.log('\n📋 Both clients getting available chunks...');
    client1.getAvailableChunks();
    client2.getAvailableChunks();
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('\n🏠 Client 1 joining room...');
    const roomId = 'test-room-' + Date.now();
    client1.joinRoom(roomId);
    
    await new Promise(resolve => setTimeout(resolve, 500));
    
    console.log('\n🏠 Client 2 joining same room...');
    client2.joinRoom(roomId);
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('\n🎮 Client 2 entering level...');
    client2.enterLevel(1, 4, '250000000000000000'); // 0.25 ETH, 4 exits
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n🎯 Client 2 selecting exit...');
    const selectedExit = Math.floor(Math.random() * 4);
    console.log(`🎲 Client 2 choosing exit ${selectedExit}...`);
    client2.selectExit(selectedExit);
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🏓 Testing ping from both clients...');
    client1.ping();
    client2.ping();
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n👋 Disconnecting clients...');
    client1.disconnect();
    client2.disconnect();
    
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    client1.disconnect();
    client2.disconnect();
  }
}

async function testSingleClient() {
  console.log('🎮 Testing LockBlock Server with Single Client\n');
  
  const client = new SimpleLockBlockClient();
  
  try {
    await client.connect();
    await client.authenticate();
    
    console.log('\n📋 Getting chunks...');
    client.getAvailableChunks();
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('\n🎮 Playing a quick game...');
    const roomId = 'solo-room-' + Date.now();
    client.joinRoom(roomId);
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    client.enterLevel(2, 5, '200000000000000000'); // Cave system, 5 exits, 0.2 ETH
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const exit = Math.floor(Math.random() * 5);
    console.log(`🎯 Selecting exit ${exit}...`);
    client.selectExit(exit);
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    client.disconnect();
    console.log('✅ Single client test completed!');
    
  } catch (error) {
    console.error('❌ Single client test failed:', error);
    client.disconnect();
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const testType = args[0] || 'single';

if (testType === 'multi') {
  testMultipleClients().catch(console.error);
} else {
  testSingleClient().catch(console.error);
}

console.log('\n💡 Usage:');
console.log('  node test-client.js        # Test with single client');
console.log('  node test-client.js multi  # Test with multiple clients');
console.log('\n🚀 Make sure the server is running first:');
console.log('  cd api-lockblock/server && npm run simple');
