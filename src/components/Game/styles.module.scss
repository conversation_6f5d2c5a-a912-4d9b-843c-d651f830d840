.gameWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  height: 100vh;
  background: linear-gradient(to right, rgba(106, 17, 203, 1), rgba(37, 117, 252, 1));
  padding: $spacing-5;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  touch-action: none;
  -webkit-overflow-scrolling: touch;
}

.gameContainer {
  width: 100%;
  max-width: 512px;
  height: 512px;
  border: 2px solid $gray-700;
  border-radius: $border-radius-2xl;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  touch-action: manipulation;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  position: relative;
}

.touchControls {
  display: flex;
  justify-content: center;
  margin-top: $spacing-5;
  gap: $spacing-5;
}

.controlButton {
  width: 120px;
  height: 120px;
  font-size: 32px;
  background-color: $gray-700;
  color: $white;
  border: 2px solid $white;
  border-radius: $border-radius-md;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;

  &:hover {
    background-color: $gray-600;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }

  &:focus {
    outline: none;
    border-color: $primary-color;
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .gameWrapper {
    padding: $spacing-3;
    min-height: 100vh;
    min-height: 100dvh;
  }

  .gameContainer {
    max-width: calc(100vw - 2 * $spacing-3);
    max-height: calc(100vw - 2 * $spacing-3);
    height: auto;
    width: auto;
    aspect-ratio: 1;
    min-width: 280px;
    min-height: 280px;
  }

  .touchControls {
    margin-top: $spacing-4;
    gap: $spacing-4;
  }

  .controlButton {
    width: 110px;
    height: 110px;
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .controlButton {
    width: 100px;
    height: 100px;
    font-size: 24px;
  }

  .touchControls {
    gap: $spacing-3;
  }
}
