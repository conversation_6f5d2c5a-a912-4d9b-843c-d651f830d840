import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { Theme } from "@radix-ui/themes";
import App from "./App";
import "./utils/i18n";
import "@radix-ui/themes/styles.css";
import "@/styles/main.scss";

const rootElement = document.getElementById("root");
if (rootElement) {
  ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <Theme>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </Theme>
    </React.StrictMode>
  );
} else {
  console.error("Root element not found");
}
