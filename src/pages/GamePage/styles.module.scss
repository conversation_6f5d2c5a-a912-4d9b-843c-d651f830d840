.gamePage {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, $primary-color, $secondary-color);
  padding: $spacing-4;
}

.title {
  color: $white;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: $spacing-6;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);

  @media (max-width: 768px) {
    font-size: 2rem;
    margin-bottom: $spacing-4;
  }

  @media (max-width: 480px) {
    font-size: 1.5rem;
    margin-bottom: $spacing-3;
  }
}
