.mainPage {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 24px;
  font-weight: bold;

  h1 {
    margin-bottom: 2rem;
  }

  .navigation {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .gameLink {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #4a90e2;
    color: white;
    text-decoration: none;
    font-size: 18px;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #3a7bc8;
    }
  }
}
