.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.mapGrid {
  display: flex;
  flex-direction: column;
  gap: 2px;
  border: 2px solid #333;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  max-width: fit-content;
}

.mapRow {
  display: flex;
  gap: 2px;
}

.mapTile {
  width: 32px;
  height: 32px;
  border: 1px solid #666;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.7);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: scale(1.1);
    border-color: #000;
    z-index: 1;
    position: relative;
  }

  &:active {
    transform: scale(0.95);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .mapTile {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
}
