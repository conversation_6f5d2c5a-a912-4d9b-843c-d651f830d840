@use "variables" as *;
@use "mixins" as *;
@import url("https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap");

html,
body,
#root {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    font-family: "Press Start 2P", monospace;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
}

// Apply fixed positioning and hidden overflow only for game page
body.game-page,
body.game-page #root {
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    image-rendering: crisp-edges;
    background-attachment: fixed;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    touch-action: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-overflow-scrolling: touch;
}

* {
    -webkit-tap-highlight-color: transparent;
    box-sizing: border-box;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

/* Pixel art styling */
.pixel-art {
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

/* Pixel borders */
.pixel-border {
    border-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="8" height="8"><rect width="8" height="8" fill="%23000" stroke="%23fff" stroke-width="1"/></svg>')
        2;
}

/* Retro button styling */
button,
.rt-Button {
    font-family: "Press Start 2P", monospace !important;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 2px solid #ff0080;
    box-shadow: 4px 4px 0px #ff0080;
    transition: all 0.1s;
    background: linear-gradient(45deg, #ff0080, #8000ff);
    color: #ffffff;

    @include mobile {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 20px;
        font-size: 10px;
        letter-spacing: 0.5px;
        border: 3px solid #ff0080;
        box-shadow: 3px 3px 0px #ff0080;
    }

    @include tablet {
        min-height: 40px;
        padding: 10px 18px;
        font-size: 11px;
        letter-spacing: 0.8px;
    }
}

button:hover,
.rt-Button:hover {
    transform: translate(2px, 2px);
    box-shadow: 2px 2px 0px #ff0080;
    filter: brightness(1.2);

    @include mobile {
        transform: translate(1px, 1px);
        box-shadow: 2px 2px 0px #ff0080;
    }
}

/* Pixel text styling */
h1,
h2,
h3,
h4,
h5,
h6,
.rt-Heading {
    font-family: "Press Start 2P", monospace !important;
    text-transform: uppercase;
    letter-spacing: 2px;
    line-height: 1.4;
    padding-top: 5vh;

    @include mobile {
        letter-spacing: 1px;
        line-height: 1.3;
        padding-top: 3vh;
        font-size: 0.8em;
    }

    @include tablet {
        letter-spacing: 1.5px;
        padding-top: 4vh;
        font-size: 0.9em;
    }
}

/* Card pixel styling */
.rt-Card {
    border: 3px solid #ff0080;
    box-shadow: 6px 6px 0px #ff0080;
    background: linear-gradient(135deg, #ffffff, #ff85e9) !important;

    @include mobile {
        border: 2px solid #ff0080;
        box-shadow: 4px 4px 0px #ff0080;
        margin-bottom: 15px;
    }

    @include tablet {
        border: 2px solid #ff0080;
        box-shadow: 5px 5px 0px #ff0080;
        margin-bottom: 20px;
    }
}

/* Badge pixel styling */
.rt-Badge {
    font-family: "Press Start 2P", monospace !important;
    border: 2px solid #ff0080;
    border-radius: 0 !important;
    text-transform: uppercase;
    font-size: 8px !important;
    background: linear-gradient(45deg, #ff0080, #8000ff) !important;
    color: #ffffff !important;
}

/* Pixel animations */
@keyframes blink {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0.5;
    }
}

@keyframes glow {
    0%,
    100% {
        text-shadow: 4px 4px 0px #000;
    }
    50% {
        text-shadow: 4px 4px 0px #000;
    }
}

@keyframes neonPulse {
    0%,
    100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Separator styling */
.rt-Separator {
    background: linear-gradient(90deg, transparent, #ff0080, #8000ff, #ff0080, transparent) !important;
    height: 3px !important;
    margin-top: 5vh;
}

/* Container styling */
.rt-Container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 20px !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;

    @include mobile {
        padding: 0 10px !important;
        max-width: 100% !important;
    }

    @include tablet {
        padding: 0 15px !important;
        max-width: 100% !important;
    }
}

/* Text styling */
.rt-Text {
    font-family: "Press Start 2P", monospace !important;
    line-height: 1.6 !important;
    color: #ff0080 !important;

    @include mobile {
        font-size: 10px !important;
        line-height: 1.5 !important;
        margin-bottom: 10px;
    }

    @include tablet {
        font-size: 11px !important;
        line-height: 1.55 !important;
        margin-bottom: 12px;
    }
}

p,
h1,
h2,
h3,
h4 {
    margin: 0;
}

a {
    text-decoration: none;
    color: inherit;
    outline: none;
}

a:hover {
    text-decoration: underline;
}
