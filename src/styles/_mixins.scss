@use "variables" as *;

// Mixins

@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
    display: flex;
    flex-direction: $direction;
    justify-content: $justify;
    align-items: $align;
    flex-wrap: $wrap;
}

@mixin flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

@mixin flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

// Responsive breakpoints
@mixin mobile {
    @media (max-width: 768px) {
        @content;
    }
}

@mixin tablet {
    @media (min-width: 769px) and (max-width: 1024px) {
        @content;
    }
}

@mixin mobile-tablet {
    @media (max-width: 1024px) {
        @content;
    }
}

@mixin desktop {
    @media (min-width: 1025px) {
        @content;
    }
}
