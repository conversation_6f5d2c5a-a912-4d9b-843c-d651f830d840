# LockBlock Testing Guide

This guide explains how to test the LockBlock server and client implementation.

## 🚀 Super Quick Start

### Option 1: Run Everything Automatically
```bash
# Install dependencies and run tests
npm run test
```

### Option 2: Manual Setup
```bash
# 1. Install server dependencies
npm run install-deps

# 2. Start the server
npm run server
# OR: ./start-lockblock.sh

# 3. In another terminal, test the client
node test-client.js
```

### Option 3: Development Mode
```bash
# Start server with auto-reload
npm run dev
```

## Quick Start

### 1. Setup Server

```bash
# Navigate to server directory
cd api-lockblock/server

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Start the simple server
npm run simple
```

The server will start on `ws://localhost:8080` and display:
```
🎮 LockBlock Server starting...
📍 Server Address: ******************************************
🔌 Port: 8080
🚀 LockBlock Server listening on port 8080
🎮 Ready for connections!
💰 Reward pool: 10000000000000000000 wei
```

### 2. Test with Client

In a new terminal:

```bash
# Single client test
node test-client.js

# Multiple clients test
node test-client.js multi
```

## What the Tests Do

### Single Client Test
1. **Connect** to the server
2. **Authenticate** with a test wallet
3. **Get available chunks** (levels)
4. **Join a room**
5. **Enter a level** with deposit
6. **Select an exit** randomly
7. **Receive game result** (win/lose)

### Multiple Clients Test
1. **Two clients connect** and authenticate
2. Both **get available chunks**
3. Both **join the same room**
4. One client **enters a level**
5. Client **selects an exit**
6. Both clients **receive game results**

## Expected Output

### Server Console
```
🎮 LockBlock Server starting...
📍 Server Address: ******************************************
🔌 Port: 8080
🚀 LockBlock Server listening on port 8080
🎮 Ready for connections!
💰 Reward pool: 10000000000000000000 wei

🔗 New connection from ::1
📨 Received: auth:request
🔐 Sent auth challenge: lockblock-auth-1703123456789-0.123456
📨 Received: auth:verify
✅ Authenticated: ******************************************
📨 Received: chunks:get
📋 Sent chunks to ******************************************
📨 Received: room:join
👤 Player ****************************************** joined room solo-room-1703123456789
📨 Received: level:enter
🎮 Level entered: chunk 2, exits: 5, correct: 3
📨 Received: exit:select
🎯 Exit selected: 1, correct: 3, winner: false
```

### Client Console
```
🎮 LockBlock Client initialized
📍 Player Address: ******************************************
🔌 Connecting to ws://localhost:8080...
✅ Connected to LockBlock server
📨 Received: welcome { message: 'Welcome to LockBlock!', serverAddress: '******************************************', timestamp: 1703123456789 }
🎉 Welcome to LockBlock!
🖥️  Server: ******************************************
🔐 Requesting authentication...
📤 Sent: auth:request
📨 Received: auth:challenge { challenge: 'lockblock-auth-1703123456789-0.123456' }
🔐 Signing challenge: lockblock-auth-1703123456789-0.123456
📝 Sent signature for verification
📤 Sent: auth:verify
📨 Received: auth:success { address: '******************************************', serverAddress: '******************************************' }
🔐 Authentication successful!

📋 Getting chunks...
📤 Sent: chunks:get
📨 Received: chunks:available {
  chunks: [
    { chunkId: 1, name: 'Forest Maze', difficulty: 'Easy', numExits: 3, minDeposit: '100000000000000000', description: 'Navigate through the forest and find the correct exit' },
    { chunkId: 2, name: 'Cave System', difficulty: 'Medium', numExits: 5, minDeposit: '200000000000000000', description: 'Explore the dark caves to discover the way out' },
    { chunkId: 3, name: 'Sky Castle', difficulty: 'Hard', numExits: 8, minDeposit: '500000000000000000', description: 'Reach the top of the floating castle and choose wisely' }
  ],
  rewardPoolBalance: '10000000000000000000',
  serverAddress: '******************************************'
}
📋 Available chunks: (3) [...]
💰 Reward pool: 10000000000000000000

🎮 Playing a quick game...
📤 Sent: room:join
📨 Received: room:joined { roomId: 'solo-room-1703123456789', chunkId: undefined, players: { server: '******************************************', player: '******************************************' }, isReady: true }
👥 Joined room: solo-room-1703123456789
📤 Sent: level:enter
📨 Received: level:entered { roomId: 'solo-room-1703123456789', gameState: { chunkId: 2, numExits: 5, playerDeposit: '200000000000000000', gamePhase: 1, playerSelectedExit: null, isPlayerWinner: null, level: { exits: [Array] } } }
🎮 Level entered! Game state: { chunkId: 2, numExits: 5, ... }
🎯 Selecting exit 1...
📤 Sent: exit:select
📨 Received: exit:selected { roomId: 'solo-room-1703123456789', exitIndex: 1, isWinner: false, reward: '0', correctExit: 3 }
🎯 Exit selection result: { roomId: 'solo-room-1703123456789', exitIndex: 1, isWinner: false, reward: '0', correctExit: 3 }
😞 You lost. Correct exit was: 3
📨 Received: game:over { isPlayerWinner: false, correctExit: 3, playerSelectedExit: 1, reward: '0', chunkId: 2 }
🏁 Game over: { isPlayerWinner: false, correctExit: 3, playerSelectedExit: 1, reward: '0', chunkId: 2 }
✅ Single client test completed!
```

## Game Flow Explanation

### 1. Authentication
- Client requests authentication challenge
- Server sends random challenge string
- Client signs challenge with private key
- Server verifies signature (simplified in demo)

### 2. Game Setup
- Client gets available chunks (levels)
- Client joins a room
- Client enters a level with deposit amount

### 3. Gameplay
- Server randomly selects correct exit (hidden from player)
- Player navigates level and selects an exit
- Server determines win/lose based on selection

### 4. Rewards
- **Win**: Player gets `deposit × numExits` from reward pool
- **Lose**: Player's deposit goes to reward pool

## Available Chunks

The server provides 3 sample chunks:

1. **Forest Maze** (Easy)
   - 3 exits, min deposit: 0.1 ETH
   - Win reward: 0.3 ETH

2. **Cave System** (Medium)
   - 5 exits, min deposit: 0.2 ETH
   - Win reward: 1.0 ETH

3. **Sky Castle** (Hard)
   - 8 exits, min deposit: 0.5 ETH
   - Win reward: 4.0 ETH

## Troubleshooting

### Server Won't Start
- Check if port 8080 is available
- Verify Node.js version (18+)
- Check environment variables

### Client Can't Connect
- Ensure server is running first
- Check WebSocket URL in client
- Verify firewall settings

### Authentication Fails
- Check private key format
- Verify ethers.js version
- Look for signature errors in console

## Next Steps

This simple implementation demonstrates:
- ✅ WebSocket communication
- ✅ Basic authentication
- ✅ Game state management
- ✅ Random exit selection
- ✅ Win/lose determination

For production, you would add:
- 🔄 Full Nitrolite integration
- 🔐 Proper signature verification
- 💾 Persistent storage
- 🎨 Frontend UI
- 🔗 Blockchain integration
- 🛡️ Security measures

## Files Overview

```
api-lockblock/
├── server/
│   ├── src/
│   │   └── simpleLockBlockServer.js    # Simple server implementation
│   ├── package.json                    # Server dependencies
│   └── .env.example                    # Environment template
├── client/
│   └── src/
│       └── simpleLockBlockClient.js    # Simple client implementation
└── test-client.js                      # Test runner
```
