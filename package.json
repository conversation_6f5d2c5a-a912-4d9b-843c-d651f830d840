{"name": "lockblock-back", "version": "1.0.0", "description": "LockBlock blockchain 2D platformer game backend", "type": "module", "main": "index.js", "scripts": {"server": "./start-lockblock.sh", "test": "./test-lockblock.sh", "install-deps": "cd api-lockblock/server && npm install", "dev": "cd api-lockblock/server && npm run simple-dev"}, "dependencies": {"ethers": "^6.7.1", "ws": "^8.14.2"}, "keywords": ["blockchain", "game", "nitrolite", "state-channels", "platformer"], "author": "LockBlock Team", "license": "MIT", "private": true}