{"name": "lockblock", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "biome check .", "preview": "vite preview"}, "dependencies": {"@erc7824/nitrolite": "0.2.6", "@radix-ui/themes": "^3.2.1", "ethers": "^6.14.3", "i18next": "^25.1.3", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.511.0", "phaser": "^3.90.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-router-dom": "^7.6.0", "uuid": "11.1.0", "viem": "2.29.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.15.18", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "10.0.0", "@vitejs/plugin-react": "^4.4.1", "globals": "^16.0.0", "path": "^0.12.7", "sass": "^1.88.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-svgr": "^4.3.0"}}